# Q - Medical AI Assistant

Q is a conversational AI platform designed for orthopedic and medical interactions, utilizing Ollama for local conversational processing.

## Features

- **Admin Dashboard**: Upload and manage knowledge base files (PDF, DOCX, TXT)
- **Interactive Conversational Frontend**: JARVIS-inspired interface with voice interaction
- **Local AI Processing**: Powered by Ollama for privacy and independence
- **Speech Features**: Voice input/output capabilities

## Tech Stack

- **Frontend**: React.js with Tailwind CSS and Three.js
- **Backend**: Python (FastAPI)
- **AI Engine**: Ollama
- **Speech Processing**: Whisper (STT) and Coqui TTS

## Setup Instructions

1. Install Ollama from [ollama.ai](https://ollama.ai)
2. Install Python dependencies:
   ```bash
   pip install -r requirements.txt
   ```
3. Install frontend dependencies:
   ```bash
   cd frontend
   npm install
   ```
4. Start the backend server:
   ```bash
   python backend/main.py
   ```
5. Start the frontend development server:
   ```bash
   cd frontend
   npm run dev
   ```

## Project Structure

```
q-project/
├── backend/           # FastAPI backend
├── frontend/         # React frontend
├── docs/            # Documentation
└── requirements.txt  # Python dependencies
```

## License

MIT License 