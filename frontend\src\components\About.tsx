import React from 'react';

const About: React.FC = () => {
  return (
    <div className="max-w-4xl mx-auto">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-900 mb-8">About Q</h1>
        <p className="text-lg text-gray-600 mb-8">
          Q is a cutting-edge conversational AI platform designed specifically for orthopedic and medical interactions.
          Built with privacy and local deployment in mind, Q leverages Ollama's powerful language models to provide
          intelligent, context-aware responses while keeping all data processing on your local machine.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-12">
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-2xl font-semibold text-gray-900 mb-4">Features</h2>
          <ul className="space-y-3">
            <li className="flex items-start">
              <span className="text-primary-500 mr-2">•</span>
              <span>Local AI processing with Ollama</span>
            </li>
            <li className="flex items-start">
              <span className="text-primary-500 mr-2">•</span>
              <span>Voice interaction capabilities</span>
            </li>
            <li className="flex items-start">
              <span className="text-primary-500 mr-2">•</span>
              <span>Knowledge base management</span>
            </li>
            <li className="flex items-start">
              <span className="text-primary-500 mr-2">•</span>
              <span>Secure document handling</span>
            </li>
          </ul>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-2xl font-semibold text-gray-900 mb-4">Technology</h2>
          <ul className="space-y-3">
            <li className="flex items-start">
              <span className="text-primary-500 mr-2">•</span>
              <span>React.js with TypeScript</span>
            </li>
            <li className="flex items-start">
              <span className="text-primary-500 mr-2">•</span>
              <span>FastAPI backend</span>
            </li>
            <li className="flex items-start">
              <span className="text-primary-500 mr-2">•</span>
              <span>Ollama for AI processing</span>
            </li>
            <li className="flex items-start">
              <span className="text-primary-500 mr-2">•</span>
              <span>Whisper for speech recognition</span>
            </li>
          </ul>
        </div>
      </div>

      <div className="mt-12 text-center">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Legal</h2>
        <div className="space-x-4">
          <a
            href="/privacy"
            className="text-primary-600 hover:text-primary-700"
          >
            Privacy Policy
          </a>
          <span className="text-gray-300">|</span>
          <a
            href="/terms"
            className="text-primary-600 hover:text-primary-700"
          >
            Terms of Use
          </a>
        </div>
      </div>
    </div>
  );
};

export default About; 