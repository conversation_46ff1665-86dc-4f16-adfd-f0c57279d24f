import os
from typing import List, Dict
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

class DocumentProcessor:
    def __init__(self):
        self.vectorizer = TfidfVectorizer()
        self.documents = []
        self.embeddings = None
        self.upload_dir = "uploads"
        os.makedirs(self.upload_dir, exist_ok=True)

    def process_document(self, file_path: str) -> None:
        """
        Process a document and add it to the search index.
        """
        # Read the document
        with open(file_path, 'r', encoding='utf-8') as f:
            text = f.read()

        # Split into chunks (simple splitting by paragraphs)
        chunks = [chunk.strip() for chunk in text.split('\n\n') if chunk.strip()]
        
        # Add to documents list
        self.documents.extend(chunks)
        
        # Create or update embeddings
        self._create_embeddings()

    def _create_embeddings(self) -> None:
        """
        Create or update the document embeddings.
        """
        if not self.documents:
            return

        # Create TF-IDF embeddings
        self.embeddings = self.vectorizer.fit_transform(self.documents)

    def search(self, query: str, k: int = 5) -> List[Dict]:
        """
        Search for relevant documents using the query.
        """
        if not self.embeddings or not self.documents:
            return []

        # Create query embedding
        query_embedding = self.vectorizer.transform([query])
        
        # Calculate similarities
        similarities = cosine_similarity(query_embedding, self.embeddings)[0]
        
        # Get top k results
        top_k_indices = np.argsort(similarities)[-k:][::-1]
        
        # Return results
        results = []
        for idx in top_k_indices:
            results.append({
                "text": self.documents[idx],
                "score": float(similarities[idx])
            })
        
        return results

    def list_documents(self) -> List[str]:
        """
        List all processed documents.
        """
        return self.documents 