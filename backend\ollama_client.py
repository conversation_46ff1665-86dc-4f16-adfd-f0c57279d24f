import requests
from typing import Dict, Any, Optional

class OllamaClient:
    def __init__(self, base_url: str = "http://localhost:11434"):
        self.base_url = base_url
        self.default_model = "llama2"  # Can be changed to any model loaded in Ollama

    def generate_response(self, prompt: str, model: Optional[str] = None) -> Dict[str, Any]:
        """
        Generate a response from Ollama using the specified model.
        """
        model = model or self.default_model
        try:
            response = requests.post(
                f"{self.base_url}/api/generate",
                json={
                    "model": model,
                    "prompt": prompt,
                    "stream": False
                }
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            raise Exception(f"Error communicating with Ollama: {str(e)}")

    def list_models(self) -> Dict[str, Any]:
        """
        List all available models in Ollama.
        """
        try:
            response = requests.get(f"{self.base_url}/api/tags")
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            raise Exception(f"Error listing Ollama models: {str(e)}")

    def chat(self, messages: list, model: Optional[str] = None) -> Dict[str, Any]:
        """
        Have a chat conversation with the model.
        """
        model = model or self.default_model
        try:
            response = requests.post(
                f"{self.base_url}/api/chat",
                json={
                    "model": model,
                    "messages": messages,
                    "stream": False
                }
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            raise Exception(f"Error in chat with Ollama: {str(e)}") 