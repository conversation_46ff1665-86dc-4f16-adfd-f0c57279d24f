import subprocess
import sys
import os
import time
import webbrowser
from threading import Thread

def run_backend():
    os.chdir("backend")
    subprocess.run([sys.executable, "main.py"])

def run_frontend():
    os.chdir("frontend")
    subprocess.run(["npm", "start"])

if __name__ == "__main__":
    # Start backend server
    backend_thread = Thread(target=run_backend)
    backend_thread.daemon = True
    backend_thread.start()

    # Wait for backend to start
    time.sleep(2)

    # Start frontend server
    frontend_thread = Thread(target=run_frontend)
    frontend_thread.daemon = True
    frontend_thread.start()

    # Wait for frontend to start
    time.sleep(5)

    # Open browser
    webbrowser.open("http://localhost:3000")

    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\nShutting down servers...")
        sys.exit(0) 