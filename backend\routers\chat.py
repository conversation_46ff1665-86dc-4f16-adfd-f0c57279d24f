from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import List, Optional
from backend.speech_processor import SpeechProcessor

router = APIRouter()
speech_processor = SpeechProcessor()

class Message(BaseModel):
    role: str
    content: str

class ChatRequest(BaseModel):
    messages: List[Message]
    audio_file: Optional[str] = None

@router.post("/chat")
async def chat(request: ChatRequest):
    try:
        # Process audio if provided
        if request.audio_file:
            text, response_audio = speech_processor.process_audio(request.audio_file)
            return {
                "response": text,
                "audio_response": response_audio
            }
        
        # Process text-only message
        return {
            "response": "I received your message. How can I help you today?",
            "audio_response": None
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/models")
async def list_models():
    try:
        models = ollama_client.list_models()
        return models
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) 