import os
import whisper
import pyttsx3
import tempfile
from typing import Tuple

class SpeechProcessor:
    def __init__(self):
        # Initialize Whisper model for speech-to-text
        self.whisper_model = whisper.load_model("base")
        
        # Initialize TTS engine
        self.tts_engine = pyttsx3.init()
        self.tts_engine.setProperty('rate', 150)  # Speed of speech
        self.tts_engine.setProperty('volume', 0.9)  # Volume (0.0 to 1.0)
        
        # Create temp directory for audio files
        self.temp_dir = tempfile.mkdtemp()

    def speech_to_text(self, audio_file_path: str) -> str:
        """
        Convert speech to text using Whisper.
        """
        try:
            result = self.whisper_model.transcribe(audio_file_path)
            return result["text"]
        except Exception as e:
            raise Exception(f"Error in speech-to-text conversion: {str(e)}")

    def text_to_speech(self, text: str) -> str:
        """
        Convert text to speech using pyttsx3.
        Returns the path to the generated audio file.
        """
        try:
            output_path = os.path.join(self.temp_dir, "output.wav")
            self.tts_engine.save_to_file(text, output_path)
            self.tts_engine.runAndWait()
            return output_path
        except Exception as e:
            raise Exception(f"Error in text-to-speech conversion: {str(e)}")

    def process_audio(self, audio_file_path: str) -> Tuple[str, str]:
        """
        Process audio file: convert to text and generate response audio.
        Returns tuple of (text, response_audio_path)
        """
        # Convert speech to text
        text = self.speech_to_text(audio_file_path)
        
        # Generate response audio
        response_audio_path = self.text_to_speech(text)
        
        return text, response_audio_path

    def cleanup(self):
        """
        Clean up temporary files.
        """
        try:
            for file in os.listdir(self.temp_dir):
                os.remove(os.path.join(self.temp_dir, file))
            os.rmdir(self.temp_dir)
        except Exception as e:
            print(f"Error cleaning up temporary files: {str(e)}") 